import requests
from bs4 import BeautifulSoup

def get_top10_from_sina(fund_code="562500", save_path="top10.txt"):
    """
    从新浪财经页面获取ETF的前十大重仓股
    """
    # 新浪 “十大重仓股” 页面
    url = f"https://stock.finance.sina.com.cn/fundInfo/view/FundInfo_ZCGP.php?symbol={fund_code}"
    headers = {"User-Agent": "Mozilla/5.0"}
    resp = requests.get(url, headers=headers, timeout=10)
    resp.encoding = resp.apparent_encoding

    if resp.status_code != 200:
        raise RuntimeError(f"请求失败，状态码: {resp.status_code}")

    html = resp.text
    soup = BeautifulSoup(html, "lxml")

    # 根据页面结构，找到显示十大重仓股的表格
    # 新浪页面里一般是一个 table，表格中有股票代码、名称、占比等
    table = soup.find("table", attrs={"class": "list_table"})  # class 名称可能要确认
    if table is None:
        # 备选：可能 class 不同，找所有 table 看哪个有“排名 股票代码 股票名称 占净值比例”等表头
        tables = soup.find_all("table")
        found = None
        for t in tables:
            header_texts = [th.get_text(strip=True) for th in t.find_all("th")]
            if "股票代码" in header_texts and "占净值比例" in header_texts:
                found = t
                break
        if found is None:
            raise ValueError("新浪页面中未找到前十大重仓股表格")
        table = found

    rows = table.find_all("tr")
    # 表头
    headers = [th.get_text(strip=True) for th in rows[0].find_all("th")]
    holdings = []
    for row in rows[1:11]:  # 前10行（假设至少有10条）
        cols = [td.get_text(strip=True) for td in row.find_all("td")]
        if len(cols) >= 3:  # 根据页面可能是代码、名称、占比
            holdings.append(cols)

    # 写文件
    with open(save_path, "w", encoding="utf-8") as f:
        f.write(f"机器人ETF（{fund_code}）最新前十大持仓（新浪）:\n")
        f.write("\t".join(headers) + "\n")
        for item in holdings:
            f.write("\t".join(item) + "\n")

    print(f"前十大持仓已保存到 {save_path}")

if __name__ == "__main__":
    get_top10_from_sina("562500", "top10.txt")
