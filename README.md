# 机器人ETF（562500）持仓查询脚本

## 功能描述
这个Python脚本可以获取机器人ETF（代码：562500）最新季度持仓的前十大股票信息，并将结果保存到文本文件中。

## 功能特点
- 🔍 自动从多个数据源获取最新持仓信息
- 📊 显示前十大持仓股票的详细信息
- 💾 自动保存结果到带时间戳的文本文件
- 🛡️ 多重数据源备份，确保数据获取成功
- 📈 包含股票代码、名称、持仓比例等完整信息

## 安装依赖
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install requests beautifulsoup4 lxml
```

## 使用方法
直接运行脚本：
```bash
python3 top10.py
```

## 输出文件
脚本会生成一个以当前时间命名的文本文件，格式如：
`robot_etf_top10_YYYYMMDD_HHMMSS.txt`

## 输出内容示例
```
============================================================
机器人ETF（562500）最新季度持仓前十大股票
============================================================
获取时间: 2025-09-15 14:27:09
ETF代码: 562500
ETF名称: 机器人ETF
------------------------------------------------------------

排名   股票代码       股票名称            持仓比例       持仓股数           
------------------------------------------------------------
1    300124     汇川技术            9.85%      1200万股         
2    002230     科大讯飞            8.76%      1100万股         
3    688169     石头科技            7.92%      1000万股         
...
```

## 数据源
脚本会尝试从以下数据源获取信息：
1. 东方财富网
2. 天天基金网
3. 新浪财经

如果网络数据源无法访问，会使用基于最新公开信息的备用数据。

## 注意事项
- 数据仅供参考，投资需谨慎
- 持仓信息通常按季度更新
- 网络连接可能影响数据获取速度

## 文件结构
- `top10.py` - 主脚本文件
- `requirements.txt` - 依赖包列表
- `README.md` - 使用说明
- `robot_etf_top10_*.txt` - 输出的持仓数据文件
